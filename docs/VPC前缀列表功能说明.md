# VPC 前缀列表功能说明

## 功能概述

本项目新增了阿里云 VPC 前缀列表管理功能，允许通过 Workers 自动管理阿里云 VPC 前缀列表中的 IP 地址。该功能与现有的 DCDN、腾讯云 VPC/CDN 功能并行工作，提供统一的 IP 地址管理接口。

## 核心特性

### 1. 智能差异更新
- **追加操作**：每次请求都是追加操作，而非覆盖操作
- **自动对比**：系统会自动对比当前 IP 列表和新 IP 列表，只更新有变化的部分
- **批量处理**：支持同时新增和删除多个 IP 地址

### 2. IP 格式处理
- **新增操作**：IP 地址可以直接使用（如 `*******`），系统会自动处理
- **删除操作**：IP 地址会自动添加 `/32` CIDR 后缀（如 `*******/32`）
- **描述信息**：所有新增的 IP 地址描述固定为 "Added by Workers"

### 3. 统一接口
- 与现有的 DCDN、腾讯云服务使用相同的接口
- 支持单个 IP 添加（`/sip` 接口）
- 支持批量 IP 管理（`/mip` 接口）

## 配置参数

需要在 KV 存储中配置以下四个参数：

| 参数名 | 说明 | 示例值 |
|--------|------|--------|
| `AliyunRegionId` | 阿里云地域 ID | `cn-shanghai` |
| `AliyunVpcPrefixListId` | VPC 前缀列表 ID | `pl-uf6oo3p1iubkggx72nxuy` |
| `AliyunVpcAccessKeyID` | 阿里云访问密钥 ID | `LTAI5t...` |
| `AliyunVpcAccessKeySecret` | 阿里云访问密钥 Secret | `xxx...` |

## API 接口

### 1. 自动添加当前 IP
```
GET/POST /sip
```
- 自动获取客户端 IP 地址并添加到所有配置的云服务中
- 返回包含 VPC 前缀列表操作结果的 JSON 响应

### 2. 管理 IP 列表
```
GET /mip
```
- 显示 IP 管理页面（需要基本认证）

```
POST /mip
```
- 批量更新 IP 列表（需要基本认证）
- 表单参数：`ipList`（每行一个 IP 地址）

## 响应格式

成功响应示例：
```json
{
  "tencentCloudVpcResponse": {...},
  "aliyunDcdnResponse": {...},
  "tencentCloudCdnResponse": {...},
  "aliyunVpcResponse": {
    "RequestId": "xxx-xxx-xxx",
    "Success": true
  },
  "msg": "*********** Added"
}
```

错误响应示例：
```json
{
  "error_msg": "500 Aliyun VPC configuration not complete"
}
```

## 技术实现

### 1. 代码架构优化
- **通用请求函数**：实现了 `aliyunRequest` 通用函数，处理所有阿里云服务的认证、签名和请求逻辑
- **代码复用**：DCDN 和 VPC 接口都复用同一套认证逻辑，减少代码重复
- **统一错误处理**：所有阿里云服务使用统一的错误处理机制

### 2. 认证方式
- 使用阿里云 ACS3-HMAC-SHA256 签名算法
- 支持不同服务的不同访问密钥配置
- 自动处理时间戳和签名随机数生成

### 3. API 调用
- **接口**：ModifyVpcPrefixList
- **版本**：2016-04-28
- **端点**：`vpc.{region}.aliyuncs.com`
- **方法**：POST

### 4. 参数构建
- 动态构建 `AddPrefixListEntry` 和 `RemovePrefixListEntry` 参数
- 支持批量操作，每个操作都有独立的索引
- 自动处理 URL 编码和参数格式化

## 日志记录

系统会记录以下操作日志：
- VPC 配置缺失警告
- IP 地址变更统计（新增/删除数量）
- API 调用错误信息

## 注意事项

1. **配置完整性**：确保所有四个 KV 配置参数都已正确设置
2. **权限要求**：阿里云访问密钥需要具有 VPC 前缀列表的修改权限
3. **地域匹配**：确保 `AliyunRegionId` 与前缀列表所在地域一致
4. **并发处理**：系统支持与其他云服务并行处理，不会相互影响

## 错误处理

常见错误及解决方案：

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| `500 Aliyun VPC configuration not complete` | KV 配置参数缺失 | 检查并设置所有必需的 KV 参数 |
| `500 Fetch error` | 网络请求失败 | 检查网络连接和 API 端点可用性 |
| `No changes needed for VPC prefix list` | IP 列表无变化 | 正常情况，无需处理 |

## 版本历史

- **v1.1.0**：代码架构优化版本
  - 实现阿里云通用请求函数 `aliyunRequest`
  - DCDN 和 VPC 接口复用统一的认证逻辑
  - 减少代码重复，提高可维护性
  - 统一错误处理机制

- **v1.0.0**：初始版本，支持基本的 VPC 前缀列表管理功能
