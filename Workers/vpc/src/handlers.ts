import { username, password, workersKV } from './config';
import { log } from './utils';
import { tencentCloudVpcRequest, aliyunDcdnRequest, tencentCloudCdnRequest, aliyunVpcRequest } from './cloudProviders';
import { renderManageIPPage } from './templates';

// 处理请求的主函数
export const handleRequest = async (request: Request): Promise<Response> => {
	const url = new URL(request.url);

	if (url.pathname === '/mip') {
		return handleManageIP(request);
	}

	if (url.pathname === '/sip') {
		return handleSetIP(request);
	}

	return new Response('{"error_msg":"404 Route Not Found"}', { status: 404 });
};

// 处理设置 IP 的请求
export const handleSetIP = async (request: Request): Promise<Response> => {
	const clientIP = request.headers.get('ali-cdn-real-ip') || 'UnknownIP';
	const ipList = await workersKV.get('ipList') || '';

	let responseData = {};

	if (!ipList.includes(clientIP)) {
		const updatedIpList = ipList ? `${ipList},${clientIP}` : clientIP;

		try {
			await workersKV.put('ipList', updatedIpList);
			const [tencentCloudVpcResponse, aliyunDcdnResponse, tencentCloudCdnResponse, aliyunVpcResponse] = await Promise.all([
				tencentCloudVpcRequest(updatedIpList),
				aliyunDcdnRequest(updatedIpList),
				tencentCloudCdnRequest(updatedIpList),
				aliyunVpcRequest(updatedIpList)
			]);
			responseData = {
				tencentCloudVpcResponse,
				aliyunDcdnResponse,
				tencentCloudCdnResponse,
				aliyunVpcResponse,
				msg: `${clientIP} Added`
			};
		} catch (error) {
			log('Error updating KV or VPC:', error instanceof Error ? error : new Error(String(error)));
			responseData = { error_msg: "500 Internal Server Error" };
		}
	} else {
		responseData = { msg: `${clientIP} Already Exists` };
	}

	return new Response(JSON.stringify(responseData), {
		status: 200,
		headers: { 'Content-Type': 'application/json' }
	});
};

// 处理管理 IP 的请求
export const handleManageIP = async (request: Request): Promise<Response> => {
	const auth = request.headers.get('Authorization');
	if (!auth || !checkAuth(auth)) {
		return new Response('{"error_msg":"401 Unauthorized"}', {
			status: 401,
			headers: { 'WWW-Authenticate': 'Basic realm="Secure Area"' }
		});
	}

	if (request.method === 'GET') {
		return renderManageIPPage();
	} else if (request.method === 'POST') {
		return updateIPList(request);
	} else {
		return new Response('{"error_msg":"405 Method Not Allowed"}', { status: 405 });
	}
};

// 更新 IP 列表
export const updateIPList = async (request: Request): Promise<Response> => {
	try {
		const formData = await request.formData();
		const ipList = formData.get('ipList')?.toString().split('\n').map(ip => ip.trim()).filter(ip => ip).join(',') || '';

		await workersKV.put('ipList', ipList);
		const [tencentCloudVpcResponse, aliyunDcdnResponse, tencentCloudCdnResponse, aliyunVpcResponse] = await Promise.all([
			tencentCloudVpcRequest(ipList),
			aliyunDcdnRequest(ipList),
			tencentCloudCdnRequest(ipList),
			aliyunVpcRequest(ipList)
		]);
		return new Response(JSON.stringify({ tencentCloudVpcResponse, aliyunDcdnResponse, tencentCloudCdnResponse, aliyunVpcResponse }), {
			status: 200,
			headers: { 'Content-Type': 'application/json' }
		});
	} catch (error) {
		log('Error updating IP list:', error instanceof Error ? error : new Error(String(error)));
		return new Response('{"error_msg":"500 Internal Server Error"}', { status: 500 });
	}
};

// 检查认证
function checkAuth(auth: string): boolean {
	if (!auth) return false;
	const [scheme, encoded] = auth.split(' ');
	if (!encoded || scheme !== 'Basic') return false;
	const decoded = atob(encoded);
	const [authUsername, authPassword] = decoded.split(':');
	return authUsername === username && authPassword === password;
}
