import { workersKV } from './config';
import { log, hmac, getHash, getDate } from './utils';

// 自定义阿里云 API 请求头
type AliyunHeaders = {
	host: string;
	'x-acs-action': string;
	'x-acs-version': string;
	'x-acs-date': string;
	'x-acs-signature-nonce': string;
	'x-acs-content-sha256': string;
	'content-type': string;
	Authorization?: string;
};

// 腾讯云通用 API 请求
export async function tencentCloudRequest(service: string, action: string, payload: string, secretIdKey: string, secretKeyKey: string, version: string): Promise<any> {
	const [secretId, secretKey, region] = await Promise.all([
		workersKV.get(secretIdKey),
		workersKV.get(secretKeyKey),
		workersKV.get('TencentCloudRegion')
	]);

	if (!secretId || !secretKey || !region) {
		log('Tencent Cloud configuration is missing');
		return { error_msg: "500 Tencent Cloud configuration not complete" };
	}

	const host = `${service}.tencentcloudapi.com`;
	const timestamp = Math.floor(Date.now() / 1000);
	const date = getDate(timestamp);

	const signedHeaders = "content-type;host";
	const hashedRequestPayload = await getHash(payload);
	const httpRequestMethod = "POST";
	const canonicalUri = "/";
	const canonicalQueryString = "";
	const canonicalHeaders = `content-type:application/json; charset=utf-8\nhost:${host}\n`;

	const canonicalRequest = `${httpRequestMethod}\n${canonicalUri}\n${canonicalQueryString}\n${canonicalHeaders}\n${signedHeaders}\n${hashedRequestPayload}`;

	const algorithm = "TC3-HMAC-SHA256";
	const hashedCanonicalRequest = await getHash(canonicalRequest);
	const credentialScope = `${date}/${service}/tc3_request`;
	const stringToSign = `${algorithm}\n${timestamp}\n${credentialScope}\n${hashedCanonicalRequest}`;

	const kDate = await hmac("TC3" + secretKey, date);
	const kService = await hmac(kDate, service);
	const kSigning = await hmac(kService, "tc3_request");
	const signature = await hmac(kSigning, stringToSign, "hex");

	const authorization = `${algorithm} Credential=${secretId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

	const headers = {
		Authorization: authorization,
		"Content-Type": "application/json; charset=utf-8",
		Host: host,
		"X-TC-Action": action,
		"X-TC-Timestamp": timestamp.toString(),
		"X-TC-Version": version,
		"X-TC-Region": region
	};

	try {
		const response = await fetch(`https://${host}`, {
			method: httpRequestMethod,
			headers,
			body: payload
		});
		return await response.json();
	} catch (error) {
		log(`Error updating Tencent Cloud ${service}:`, error instanceof Error ? error : new Error(String(error)));
		return { error_msg: "500 Fetch error" };
	}
}

// 腾讯云 VPC 请求
export async function tencentCloudVpcRequest(ipList: string): Promise<any> {
	const addressTemplateId = await workersKV.get('TencentCloudVpcAddressTemplateId');
	if (!addressTemplateId) {
		log('Tencent Cloud AddressTemplateId is missing');
		return { error_msg: "500 Tencent Cloud AddressTemplateId not complete" };
	}

	const addressesExtra = ipList.split(',').map(ip => ({ Address: ip, Description: "Added by Workers" }));
	const payload = JSON.stringify({
		AddressTemplateId: addressTemplateId,
		AddressesExtra: addressesExtra
	});

	return tencentCloudRequest('vpc', 'ModifyAddressTemplateAttribute', payload, 'TencentCloudVpcSecretId', 'TencentCloudVpcSecretKey', '2017-03-12');
}

// 腾讯云 CDN 请求
export async function tencentCloudCdnRequest(ipList: string): Promise<any> {
	const tencentCloudCdnDomainArrayStr = await workersKV.get('TencentCloudCdnDomainArray');
	const tencentCloudCdnDomainArray = JSON.parse(tencentCloudCdnDomainArrayStr || '[]');

	if (!tencentCloudCdnDomainArray.length) {
		log('Tencent Cloud DomainArray is missing');
		return { error_msg: "500 Tencent Cloud DomainArray not complete" };
	}

	const results = [];

	for (const domain of tencentCloudCdnDomainArray) {
		const payload = JSON.stringify({
			Domain: domain,
			IpFilter: {
				Switch: "on",
				FilterType: "whitelist",
				Filters: ipList.split(',')
			}
		});

		const result = await tencentCloudRequest('cdn', 'UpdateDomainConfig', payload, 'TencentCloudCdnSecretId', 'TencentCloudCdnSecretKey', '2018-06-06');
		results.push({ domain, response: result });
	}

	return results;
}

// 阿里云通用 API 请求
export async function aliyunRequest(service: string, action: string, version: string, body: string, accessKeyIdKey: string, accessKeySecretKey: string, region?: string): Promise<any> {
	const [accessKeyID, accessKeySecret] = await Promise.all([
		workersKV.get(accessKeyIdKey),
		workersKV.get(accessKeySecretKey)
	]);

	if (!accessKeyID || !accessKeySecret) {
		log(`Aliyun ${service} configuration is incomplete`);
		return { error_msg: `500 Aliyun ${service} configuration not complete` };
	}

	const timestamp = new Date().toISOString().replace(/\.\d{3}Z$/, 'Z');
	const signatureNonce = crypto.randomUUID();
	const host = region ? `${service}.${region}.aliyuncs.com` : `${service}.aliyuncs.com`;
	const method = 'POST';
	const path = '/';

	const contentSha256 = await getHash(body);

	const headers: AliyunHeaders = {
		'host': host,
		'x-acs-action': action,
		'x-acs-version': version,
		'x-acs-date': timestamp,
		'x-acs-signature-nonce': signatureNonce,
		'x-acs-content-sha256': contentSha256,
		'content-type': 'application/x-www-form-urlencoded'
	};

	const canonicalizedHeaders = Object.keys(headers)
		.sort()
		.map(key => `${key}:${headers[key as keyof AliyunHeaders]}`)
		.join('\n') + '\n';

	const signedHeaders = Object.keys(headers).sort().join(';');

	const canonicalRequest = `${method}\n${path}\n\n${canonicalizedHeaders}\n${signedHeaders}\n${contentSha256}`;

	const stringToSign = `ACS3-HMAC-SHA256\n${await getHash(canonicalRequest)}`;

	const signature = await hmac(accessKeySecret, stringToSign, 'hex');

	(headers as AliyunHeaders)['Authorization'] = `ACS3-HMAC-SHA256 Credential=${accessKeyID},SignedHeaders=${signedHeaders},Signature=${signature}`;

	try {
		const response = await fetch(`https://${host}${path}`, {
			method,
			headers,
			body
		});
		return await response.json();
	} catch (error) {
		log(`Aliyun ${service} fetch error:`, error instanceof Error ? error : new Error(String(error)));
		return { error_msg: "500 Fetch error" };
	}
}

// 阿里云 DCDN 请求
export async function aliyunDcdnRequest(ipList: string): Promise<any> {
	const aliyunDcdnDomainArrayStr = await workersKV.get('AliyunDcdnDomainArray');
	const aliyunDcdnDomainArray = JSON.parse(aliyunDcdnDomainArrayStr || '[]');

	if (!aliyunDcdnDomainArray.length) {
		log('Aliyun DCDN DomainArray is missing');
		return { error_msg: "500 Aliyun DCDN DomainArray not complete" };
	}

	const domainNames = aliyunDcdnDomainArray.join(',');
	const functions = JSON.stringify([{
		functionArgs: [{ argName: 'ip_list', argValue: ipList }],
		functionName: 'ip_allow_list_set'
	}]);

	const body = `DomainNames=${encodeURIComponent(domainNames)}&Functions=${encodeURIComponent(functions)}`;

	return aliyunRequest('dcdn', 'BatchSetDcdnDomainConfigs', '2018-01-15', body, 'AliyunDcdnAccessKeyID', 'AliyunDcdnAccessKeySecret');
}

// 阿里云 VPC 前缀列表请求
export async function aliyunVpcRequest(newIpList: string): Promise<any> {
	const [aliyunRegionId, aliyunVpcPrefixListId, currentIpListStr] = await Promise.all([
		workersKV.get('AliyunRegionId'),
		workersKV.get('AliyunVpcPrefixListId'),
		workersKV.get('ipList')
	]);

	if (!aliyunRegionId || !aliyunVpcPrefixListId) {
		log('Aliyun VPC configuration is incomplete');
		return { error_msg: "500 Aliyun VPC configuration not complete" };
	}

	// 计算需要新增和删除的IP
	const currentIpArray = currentIpListStr ? currentIpListStr.split(',').filter(ip => ip.trim()) : [];
	const newIpArray = newIpList ? newIpList.split(',').filter(ip => ip.trim()) : [];

	const toAdd = newIpArray.filter(ip => !currentIpArray.includes(ip));
	const toRemove = currentIpArray.filter(ip => !newIpArray.includes(ip));

	// 如果没有变化，直接返回
	if (toAdd.length === 0 && toRemove.length === 0) {
		return { msg: "No changes needed for VPC prefix list" };
	}

	// 构建请求参数
	const params: string[] = [
		`RegionId=${encodeURIComponent(aliyunRegionId)}`,
		`PrefixListId=${encodeURIComponent(aliyunVpcPrefixListId)}`
	];

	// 添加新增IP参数
	toAdd.forEach((ip, index) => {
		params.push(`AddPrefixListEntry.${index + 1}.Cidr=${encodeURIComponent(ip)}`);
		params.push(`AddPrefixListEntry.${index + 1}.Description=${encodeURIComponent('Added by Workers')}`);
	});

	// 添加删除IP参数（需要添加/32后缀）
	toRemove.forEach((ip, index) => {
		const cidr = ip.includes('/') ? ip : `${ip}/32`;
		params.push(`RemovePrefixListEntry.${index + 1}.Cidr=${encodeURIComponent(cidr)}`);
	});

	const body = params.join('&');

	try {
		const result = await aliyunRequest('vpc', 'ModifyVpcPrefixList', '2016-04-28', body, 'AliyunVpcAccessKeyID', 'AliyunVpcAccessKeySecret', aliyunRegionId);

		// 记录操作详情
		if (toAdd.length > 0 || toRemove.length > 0) {
			log(`VPC prefix list updated: added ${toAdd.length} IPs, removed ${toRemove.length} IPs`);
		}

		return result;
	} catch (error) {
		log('Aliyun VPC request error:', error instanceof Error ? error : new Error(String(error)));
		return { error_msg: "500 Request error" };
	}
}
