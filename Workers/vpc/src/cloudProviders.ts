import { workersKV } from './config';
import { log, hmac, getHash, getDate } from './utils';

// 自定义阿里云 API 请求头
type AliyunHeaders = {
	host: string;
	'x-acs-action': string;
	'x-acs-version': string;
	'x-acs-date': string;
	'x-acs-signature-nonce': string;
	'x-acs-content-sha256': string;
	'content-type': string;
	Authorization?: string;
};

// 腾讯云 API 请求
export async function tencentCloudRequest(service: string, action: string, payload: string, secretIdKey: string, secretKeyKey: string, version: string): Promise<any> {
	const [secretId, secretKey, region] = await Promise.all([
		workersKV.get(secretIdKey),
		workersKV.get(secretKeyKey),
		workersKV.get('TencentCloudRegion')
	]);

	if (!secretId || !secretKey || !region) {
		log('Tencent Cloud configuration is missing');
		return { error_msg: "500 Tencent Cloud configuration not complete" };
	}

	const host = `${service}.tencentcloudapi.com`;
	const timestamp = Math.floor(Date.now() / 1000);
	const date = getDate(timestamp);

	const signedHeaders = "content-type;host";
	const hashedRequestPayload = await getHash(payload);
	const httpRequestMethod = "POST";
	const canonicalUri = "/";
	const canonicalQueryString = "";
	const canonicalHeaders = `content-type:application/json; charset=utf-8\nhost:${host}\n`;

	const canonicalRequest = `${httpRequestMethod}\n${canonicalUri}\n${canonicalQueryString}\n${canonicalHeaders}\n${signedHeaders}\n${hashedRequestPayload}`;

	const algorithm = "TC3-HMAC-SHA256";
	const hashedCanonicalRequest = await getHash(canonicalRequest);
	const credentialScope = `${date}/${service}/tc3_request`;
	const stringToSign = `${algorithm}\n${timestamp}\n${credentialScope}\n${hashedCanonicalRequest}`;

	const kDate = await hmac("TC3" + secretKey, date);
	const kService = await hmac(kDate, service);
	const kSigning = await hmac(kService, "tc3_request");
	const signature = await hmac(kSigning, stringToSign, "hex");

	const authorization = `${algorithm} Credential=${secretId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

	const headers = {
		Authorization: authorization,
		"Content-Type": "application/json; charset=utf-8",
		Host: host,
		"X-TC-Action": action,
		"X-TC-Timestamp": timestamp.toString(),
		"X-TC-Version": version,
		"X-TC-Region": region
	};

	try {
		const response = await fetch(`https://${host}`, {
			method: httpRequestMethod,
			headers,
			body: payload
		});
		return await response.json();
	} catch (error) {
		log(`Error updating Tencent Cloud ${service}:`, error instanceof Error ? error : new Error(String(error)));
		return { error_msg: "500 Fetch error" };
	}
}

// 腾讯云 VPC 请求
export async function tencentCloudVpcRequest(ipList: string): Promise<any> {
	const addressTemplateId = await workersKV.get('TencentCloudVpcAddressTemplateId');
	if (!addressTemplateId) {
		log('Tencent Cloud AddressTemplateId is missing');
		return { error_msg: "500 Tencent Cloud AddressTemplateId not complete" };
	}

	const addressesExtra = ipList.split(',').map(ip => ({ Address: ip, Description: "Added by Workers" }));
	const payload = JSON.stringify({
		AddressTemplateId: addressTemplateId,
		AddressesExtra: addressesExtra
	});

	return tencentCloudRequest('vpc', 'ModifyAddressTemplateAttribute', payload, 'TencentCloudVpcSecretId', 'TencentCloudVpcSecretKey', '2017-03-12');
}

// 腾讯云 CDN 请求
export async function tencentCloudCdnRequest(ipList: string): Promise<any> {
	const tencentCloudCdnDomainArrayStr = await workersKV.get('TencentCloudCdnDomainArray');
	const tencentCloudCdnDomainArray = JSON.parse(tencentCloudCdnDomainArrayStr || '[]');

	if (!tencentCloudCdnDomainArray.length) {
		log('Tencent Cloud DomainArray is missing');
		return { error_msg: "500 Tencent Cloud DomainArray not complete" };
	}

	const results = [];

	for (const domain of tencentCloudCdnDomainArray) {
		const payload = JSON.stringify({
			Domain: domain,
			IpFilter: {
				Switch: "on",
				FilterType: "whitelist",
				Filters: ipList.split(',')
			}
		});

		const result = await tencentCloudRequest('cdn', 'UpdateDomainConfig', payload, 'TencentCloudCdnSecretId', 'TencentCloudCdnSecretKey', '2018-06-06');
		results.push({ domain, response: result });
	}

	return results;
}

// 阿里云 DCDN 请求
export async function aliyunDcdnRequest(ipList: string): Promise<any> {
	const [aliyunDcdnAccessKeyID, aliyunDcdnAccessKeySecret, aliyunDcdnDomainArrayStr] = await Promise.all([
		workersKV.get('AliyunDcdnAccessKeyID'),
		workersKV.get('AliyunDcdnAccessKeySecret'),
		workersKV.get('AliyunDcdnDomainArray')
	]);
	const aliyunDcdnDomainArray = JSON.parse(aliyunDcdnDomainArrayStr || '[]');

	if (!aliyunDcdnAccessKeyID || !aliyunDcdnAccessKeySecret || !aliyunDcdnDomainArray.length) {
		log('Aliyun configuration is incomplete');
		return { error_msg: "500 Aliyun configuration not complete" };
	}

	const action = 'BatchSetDcdnDomainConfigs';
	const version = '2018-01-15';
	const timestamp = new Date().toISOString().replace(/\.\d{3}Z$/, 'Z');
	const signatureNonce = crypto.randomUUID();
	const host = 'dcdn.aliyuncs.com';
	const method = 'POST';
	const path = '/';

	const domainNames = aliyunDcdnDomainArray.join(',');
	const functions = JSON.stringify([{
		functionArgs: [{ argName: 'ip_list', argValue: ipList }],
		functionName: 'ip_allow_list_set'
	}]);

	const body = `DomainNames=${encodeURIComponent(domainNames)}&Functions=${encodeURIComponent(functions)}`;

	const contentSha256 = await getHash(body);

	const headers: AliyunHeaders = {
		'host': host,
		'x-acs-action': action,
		'x-acs-version': version,
		'x-acs-date': timestamp,
		'x-acs-signature-nonce': signatureNonce,
		'x-acs-content-sha256': contentSha256,
		'content-type': 'application/x-www-form-urlencoded'
	};

	const canonicalizedHeaders = Object.keys(headers)
		.sort()
		.map(key => `${key}:${headers[key as keyof AliyunHeaders]}`)
		.join('\n') + '\n';

	const signedHeaders = Object.keys(headers).sort().join(';');

	const canonicalRequest = `${method}\n${path}\n\n${canonicalizedHeaders}\n${signedHeaders}\n${contentSha256}`;

	const stringToSign = `ACS3-HMAC-SHA256\n${await getHash(canonicalRequest)}`;

	const signature = await hmac(aliyunDcdnAccessKeySecret, stringToSign, 'hex');

	(headers as AliyunHeaders)['Authorization'] = `ACS3-HMAC-SHA256 Credential=${aliyunDcdnAccessKeyID},SignedHeaders=${signedHeaders},Signature=${signature}`;

	try {
		const response = await fetch(`https://${host}${path}`, {
			method,
			headers,
			body
		});
		return await response.json();
	} catch (error) {
		log('Aliyun DCDN fetch error:', error instanceof Error ? error : new Error(String(error)));
		return { error_msg: "500 Fetch error" };
	}
}
